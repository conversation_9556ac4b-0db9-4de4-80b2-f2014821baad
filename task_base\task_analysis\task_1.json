{"task_id": "1", "description": "Inspect the library roof for any water damage or structural issues.\n\nProfessional context: Conduct a high-precision structural inspection of the library roof to generate a detailed damage assessment report, requiring clear identification of micro-cracks and potential leakage points.\n\nUser context: The rainy season is coming, so we must file a detailed report on the roof's condition. You need to fly up there, capture every corner, and I need to be able to zoom in to see even the tiniest cracks.", "home_position": [52.18293527764432, 91.67762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149]]}, "task_types": ["Structure scan task"], "subtasks": [{"subtask_id": "1", "type": "Structure scan task", "target_area_id": "area_1", "target_area_name": "library", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[52.178187687906515, 91.68101263046265], [52.17856612739797, 91.68177437782289], [52.17805834996039, 91.68223571777345], [52.17766273150595, 91.68148469924928]]]}, "parameters": {"frontal_overlap": 88.0, "lateral_overlap": 78.0, "gsd": 0.8, "wtask": 0.9, "wsemantic": 0.95, "height": 15}, "error": null}]}