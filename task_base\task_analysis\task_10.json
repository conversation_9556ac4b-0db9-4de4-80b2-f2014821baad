{"task_id": "10", "description": "Inspect the cafeteria roof and surrounding area for potential maintenance issues.\n\nProfessional context: Conduct a standard inspection of the cafeteria roof and its surrounding ancillary facilities (e.g., exhaust ducts, AC units) to identify all visible potential maintenance risks and generate an inspection checklist.\n\nUser context: It's safety inspection month again. Can you check the cafeteria roof and all those pipes and equipment next to it? Look for anything loose or rusty so I can fill out the report form.", "home_position": [52.18293527764432, 91.67762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149]]}, "task_types": ["Structure scan task"], "subtasks": [{"subtask_id": "1", "type": "Structure scan task", "target_area_id": "area_5", "target_area_name": "cafeteria", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[52.18353694927753, 91.67615246772768], [52.18351974863444, 91.67742919921876], [52.18295181578062, 91.67743992805482], [52.18295181578062, 91.67614173889162]]]}, "parameters": {"frontal_overlap": 83.0, "lateral_overlap": 73.0, "gsd": 1.5, "wtask": 0.7, "wsemantic": 0.6, "height": 12}, "error": null}]}