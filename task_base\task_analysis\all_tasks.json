[{"task_id": "1", "description": "Inspect the library roof for any water damage or structural issues.\n\nProfessional context: Conduct a high-precision structural inspection of the library roof to generate a detailed damage assessment report, requiring clear identification of micro-cracks and potential leakage points.\n\nUser context: The rainy season is coming, so we must file a detailed report on the roof's condition. You need to fly up there, capture every corner, and I need to be able to zoom in to see even the tiniest cracks.", "home_position": [52.18293527764432, 91.67762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149]]}, "task_types": ["Structure scan task"], "subtasks": [{"subtask_id": "1", "type": "Structure scan task", "target_area_id": "area_1", "target_area_name": "library", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[52.178187687906515, 91.68101263046265], [52.17856612739797, 91.68177437782289], [52.17805834996039, 91.68223571777345], [52.17766273150595, 91.68148469924928]]]}, "parameters": {"frontal_overlap": 88.0, "lateral_overlap": 78.0, "gsd": 0.8, "wtask": 0.9, "wsemantic": 0.95, "height": 15}, "error": null}]}, {"task_id": "2", "description": "Check the condition of the football field and track to identify any areas needing maintenance. Professional context: Perform a comprehensive health assessment of the football field turf and track surface, identifying and precisely annotating all areas requiring maintenance for the annual detailed maintenance plan. User context: It's time to report the year-end budget. Can you help me survey the entire field in detail? Mark out any bald patches on the grass or damage on the track, otherwise, I can't ask management for money to fix it next year.", "home_position": [52.18293527764432, 91.67762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149]]}, "task_types": ["Survey task"], "subtasks": [{"subtask_id": "1", "type": "Survey task", "target_area_id": "area_6", "target_area_name": "track_field", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[52.18701182991439, 91.67929601669313], [52.18700057924048, 91.68035817146303], [52.18537879391133, 91.68037962913515], [52.1853916940822, 91.67927992343904]]]}, "parameters": {"frontal_overlap": 77.0, "lateral_overlap": 67.0, "gsd": 2.4, "wtask": 0.8, "wsemantic": 0.9}, "error": null}, {"subtask_id": "2", "type": "Survey task", "target_area_id": "area_7", "target_area_name": "football_field", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[52.18675374050383, 91.6795427799225], [52.186746786132424, 91.68018651008607], [52.18558958016828, 91.68018651008607], [52.18560097667821, 91.67953205108644]]]}, "parameters": {"frontal_overlap": 77.0, "lateral_overlap": 67.0, "gsd": 2.4, "wtask": 0.8, "wsemantic": 0.9}, "error": null}]}, {"task_id": "3", "description": "Survey the dormitory area to assess external building conditions and nearby facilities.\n\nProfessional context: Conduct a rapid survey of the dormitory area to assess the general condition of building facades and surrounding public facilities, generating an overview report.\n\nUser context: I need this for a meeting this afternoon. Can you quickly fly over to the dorms and just snap a few photos? Just a rough look to see if there are any major issues with the buildings and roads is fine. No need for detail, just give me a general idea.", "home_position": [52.18293527764432, 91.67762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149]]}, "task_types": ["Survey task", "Structure scan task"], "subtasks": [{"subtask_id": "1", "type": "Survey task", "target_area_id": "area_4", "target_area_name": "dormitory_area", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[52.184655918509605, 91.6771663427353], [52.18466444224811, 91.67742383480073], [52.18447501200689, 91.6774184703827], [52.18447067549101, 91.67715561389925]]]}, "parameters": {"frontal_overlap": 76.0, "lateral_overlap": 66.0, "gsd": 3.2, "wtask": -0.3, "wsemantic": -0.7}, "error": null}, {"subtask_id": "2", "type": "Structure scan task", "target_area_id": "area_4", "target_area_name": "dormitory_area", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[52.184655918509605, 91.6771663427353], [52.18466444224811, 91.67742383480073], [52.18447501200689, 91.6774184703827], [52.18447067549101, 91.67715561389925]]]}, "parameters": {"frontal_overlap": 76.0, "lateral_overlap": 66.0, "gsd": 3.2, "wtask": -0.4, "wsemantic": -0.7, "height": 9}, "error": null}, {"subtask_id": "3", "type": "Survey task", "target_area_id": "area_10", "target_area_name": "main_road", "feasible": true, "geometry": {"type": "LineString", "coordinates": [[52.18446610232326, 91.67749893665315], [52.18295674948406, 91.67763841152193], [52.18293094832333, 91.67923164367677]]}, "parameters": {"frontal_overlap": 76.0, "lateral_overlap": 66.0, "gsd": 3.2, "wtask": -0.5, "wsemantic": -0.7}, "error": null}]}, {"task_id": "4", "description": "Capture high-resolution photos of Yingxue Lake to monitor water levels and surrounding vegetation. Professional context: Acquire multi-angle, high-resolution imagery of Yingxue Lake for precise analysis of the current water line, water eutrophication levels, and shoreline vegetation coverage and health. The data will be used for academic research. User context: The data for this project is critical; we need to analyze the relationship between the lake water and the surrounding vegetation. Fly over there and capture the water color, water level, and the condition of the plants on the shore crystal clear. Not a single detail can be missed.", "home_position": [52.18293527764432, 91.67762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149]]}, "task_types": ["Survey task"], "subtasks": [{"subtask_id": "1", "type": "Survey task", "target_area_id": "area_9", "target_area_name": "yingxue_lake", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[52.182737027923125, 91.67796564102174], [52.18272842751261, 91.67910289764406], [52.181420775275065, 91.67902779579164], [52.181425275805104, 91.67798709869386]]]}, "parameters": {"frontal_overlap": 76.0, "lateral_overlap": 66.0, "gsd": 2.5, "wtask": 0.8, "wsemantic": 0.9}, "error": null}]}, {"task_id": "5", "description": "Create a detailed map of the basketball courts. Professional context: Perform an orthomosaic survey of the basketball courts to generate an ultra-high-precision map with a GSD no worse than 1.6 cm/pixel, intended for the fine analysis of ground line wear. User context: We need to issue a formal report on the basketball court's maintenance, and we need the absolute clearest photo as a base map. It must be sharp enough to see every single scratch and the tiniest crack on the ground. There's zero room for ambiguity.", "home_position": [52.18293527764432, 91.67762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149]]}, "task_types": ["Survey task"], "subtasks": [{"subtask_id": "1", "type": "Survey task", "target_area_id": "area_8", "target_area_name": "basketball_courts", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[52.18579146905999, 91.67681765556335], [52.185804369161644, 91.67740237712862], [52.185270955633385, 91.67740774154665], [52.18525805544227, 91.67681229114534]]]}, "parameters": {"frontal_overlap": 80.0, "lateral_overlap": 70.0, "gsd": 1.6, "wtask": 0.9, "wsemantic": 1.0}, "error": null}]}, {"task_id": "7", "description": "Perform a comprehensive scan of the teaching building exterior.\n\nProfessional context: Execute a high-precision 3D reconstruction scan of the teaching building's facade. Ensure complete data integrity for archival purposes.\n\nUser context: We need to create a digital model of this teaching building for our archives, so every facade and every corner must be captured. Nothing can be missed, to ensure the final model is both complete and accurate.", "home_position": [52.18293527764432, 91.67762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149]]}, "task_types": ["Structure scan task"], "subtasks": [{"subtask_id": "1", "type": "Structure scan task", "target_area_id": "area_2", "target_area_name": "teaching_building", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[52.18024313436918, 91.68204259872437], [52.180569959852235, 91.68287944793703], [52.17979537294124, 91.68333005905153], [52.179399887214934, 91.6825683116913]]]}, "parameters": {"frontal_overlap": 89.0, "lateral_overlap": 79.0, "gsd": 0.8, "wtask": 0.9, "wsemantic": 0.95, "height": 20}, "error": null}]}, {"task_id": "8", "description": "Fly along the main road and capture photos to document campus traffic flow. Professional context: Execute a rapid fly-through along the main road to roughly document peak-hour traffic flow. The objective is to capture the general traffic distribution without need for fine-detail recognition. User context: I just want to see how congested the main road gets during the evening rush hour. Just do a quick pass from one end to the other. I just need a general idea, no need to take careful shots.", "home_position": [52.18293527764432, 91.67762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149]]}, "task_types": ["Corridor scan task"], "subtasks": [{"subtask_id": "1", "type": "Corridor scan task", "target_area_id": "area_10", "target_area_name": "main_road", "feasible": true, "geometry": {"type": "LineString", "coordinates": [[52.18446610232326, 91.67749893665315], [52.18295674948406, 91.67763841152193], [52.18293094832333, 91.67923164367677]]}, "parameters": {"frontal_overlap": 62.0, "lateral_overlap": 52.0, "gsd": 5.2, "wtask": -0.3, "wsemantic": -0.7, "width": 8}, "error": null}]}, {"task_id": "9", "description": "Photograph the laboratory building from multiple angles to document its current state.\n\nProfessional context: Capture a set of high-quality imagery of the laboratory building via multi-angle, multi-altitude orbital flights to comprehensively document the building's current external condition for submission to the engineering department.\n\nUser context: The engineering department needs a status report on the lab building. Please help me get shots of all sides of the building, make them clear, and ensure the information is complete so they can't say our submission is inadequate.", "home_position": [52.18293527764432, 91.67762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149]]}, "task_types": ["Structure scan task"], "subtasks": [{"subtask_id": "1", "type": "Structure scan task", "target_area_id": "area_3", "target_area_name": "laboratory_building", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[52.181799544748664, 91.6798861026764], [52.18178233279426, 91.68063712120056], [52.181275563282455, 91.68061566352846], [52.181257484665764, 91.6798861026764]]]}, "parameters": {"frontal_overlap": 85.0, "lateral_overlap": 75.0, "gsd": 1.5, "wtask": 0.8, "wsemantic": 0.9, "height": 18}, "error": null}]}, {"task_id": "10", "description": "Inspect the cafeteria roof and surrounding area for potential maintenance issues.\n\nProfessional context: Conduct a standard inspection of the cafeteria roof and its surrounding ancillary facilities (e.g., exhaust ducts, AC units) to identify all visible potential maintenance risks and generate an inspection checklist.\n\nUser context: It's safety inspection month again. Can you check the cafeteria roof and all those pipes and equipment next to it? Look for anything loose or rusty so I can fill out the report form.", "home_position": [52.18293527764432, 91.67762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149]]}, "task_types": ["Structure scan task"], "subtasks": [{"subtask_id": "1", "type": "Structure scan task", "target_area_id": "area_5", "target_area_name": "cafeteria", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[52.18353694927753, 91.67615246772768], [52.18351974863444, 91.67742919921876], [52.18295181578062, 91.67743992805482], [52.18295181578062, 91.67614173889162]]]}, "parameters": {"frontal_overlap": 83.0, "lateral_overlap": 73.0, "gsd": 1.5, "wtask": 0.7, "wsemantic": 0.6, "height": 12}, "error": null}]}, {"task_id": "11", "description": "Create an orthomosaic map of the entire campus.\n\nProfessional context: Execute a campus-wide aerial survey mission to generate a complete, seamlessly stitched, high-precision orthomosaic map, to be used for future digital campus planning.\n\nUser context: This is one of the most important projects for our university, to create a brand new, definitive digital map. You must cover the entire campus, and the final stitched map cannot have any misalignments or blurring. All future planning will rely on this map.", "home_position": [52.18293527764432, 91.67762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149]]}, "task_types": ["Survey task"], "subtasks": [{"subtask_id": "1", "type": "Survey task", "target_area_id": "campus_entire", "target_area_name": "Entire Campus", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149], [52.18772685877701, 91.67438220977783]]]}, "parameters": {"frontal_overlap": 90.0, "lateral_overlap": 80.0, "gsd": 0.8, "wtask": 0.8, "wsemantic": 0.9}, "error": null}]}, {"task_id": "12", "description": "Capture aerial photos of the campus entrance and main roads for campus navigation materials.\n\nProfessional context: To produce campus navigation materials, please capture aerial imagery of the main campus entrance and key roads. The imagery should be well-composed and clear, explicitly showing road directions and landmark buildings.\n\nUser context: We want to create a nice and practical navigation guide for new students. Can you help me get some shots of the main gate and the main roads? They need to be clear enough for people to understand the layout at a glance.", "home_position": [52.18293527764432, 91.67762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149]]}, "task_types": ["Corridor scan task", "Structure scan task"], "subtasks": [{"subtask_id": "1", "type": "Corridor scan task", "target_area_id": "area_10", "target_area_name": "main_road", "feasible": true, "geometry": {"type": "LineString", "coordinates": [[52.18446610232326, 91.67749893665315], [52.18295674948406, 91.67763841152193], [52.18293094832333, 91.67923164367677]]}, "parameters": {"frontal_overlap": 74.0, "lateral_overlap": 64.0, "gsd": 3.0, "wtask": 0.3, "wsemantic": 0.6, "width": 8}, "error": null}, {"subtask_id": "2", "type": "Structure scan task", "target_area_id": "area_11", "target_area_name": "campus_entrance", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[52.18380008507551, 91.67593252658844], [52.18415272897228, 91.67593252658844], [52.184152721690644, 91.67609882354736], [52.18380420682054, 91.67609882354736]]]}, "parameters": {"frontal_overlap": 74.0, "lateral_overlap": 64.0, "gsd": 2.8, "wtask": 0.5, "wsemantic": 0.6, "height": null}, "error": null}]}]