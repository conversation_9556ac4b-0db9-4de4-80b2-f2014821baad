{"task_id": "2", "description": "Check the condition of the football field and track to identify any areas needing maintenance. Professional context: Perform a comprehensive health assessment of the football field turf and track surface, identifying and precisely annotating all areas requiring maintenance for the annual detailed maintenance plan. User context: It's time to report the year-end budget. Can you help me survey the entire field in detail? Mark out any bald patches on the grass or damage on the track, otherwise, I can't ask management for money to fix it next year.", "home_position": [52.18293527764432, 91.67762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149]]}, "task_types": ["Survey task"], "subtasks": [{"subtask_id": "1", "type": "Survey task", "target_area_id": "area_6", "target_area_name": "track_field", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[52.18701182991439, 91.67929601669313], [52.18700057924048, 91.68035817146303], [52.18537879391133, 91.68037962913515], [52.1853916940822, 91.67927992343904]]]}, "parameters": {"frontal_overlap": 77.0, "lateral_overlap": 67.0, "gsd": 2.4, "wtask": 0.8, "wsemantic": 0.9}, "error": null}, {"subtask_id": "2", "type": "Survey task", "target_area_id": "area_7", "target_area_name": "football_field", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[52.18675374050383, 91.6795427799225], [52.186746786132424, 91.68018651008607], [52.18558958016828, 91.68018651008607], [52.18560097667821, 91.67953205108644]]]}, "parameters": {"frontal_overlap": 77.0, "lateral_overlap": 67.0, "gsd": 2.4, "wtask": 0.8, "wsemantic": 0.9}, "error": null}]}