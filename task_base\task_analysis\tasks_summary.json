[{"task_id": "1", "description": "Inspect the library roof for any water damage or structural issues.\n\nProfessional context: Conduct a high-precision structural inspection of the library roof to generate a detailed damage assessment report, requiring clear identification of micro-cracks and potential leakage points.\n\nUser context: The rainy season is coming, so we must file a detailed report on the roof's condition. You need to fly up there, capture every corner, and I need to be able to zoom in to see even the tiniest cracks.", "task_types": ["Structure scan task"], "subtask_count": 1}, {"task_id": "2", "description": "Check the condition of the football field and track to identify any areas needing maintenance. Professional context: Perform a comprehensive health assessment of the football field turf and track surface, identifying and precisely annotating all areas requiring maintenance for the annual detailed maintenance plan. User context: It's time to report the year-end budget. Can you help me survey the entire field in detail? Mark out any bald patches on the grass or damage on the track, otherwise, I can't ask management for money to fix it next year.", "task_types": ["Survey task"], "subtask_count": 2}, {"task_id": "3", "description": "Survey the dormitory area to assess external building conditions and nearby facilities.\n\nProfessional context: Conduct a rapid survey of the dormitory area to assess the general condition of building facades and surrounding public facilities, generating an overview report.\n\nUser context: I need this for a meeting this afternoon. Can you quickly fly over to the dorms and just snap a few photos? Just a rough look to see if there are any major issues with the buildings and roads is fine. No need for detail, just give me a general idea.", "task_types": ["Survey task", "Structure scan task"], "subtask_count": 3}, {"task_id": "4", "description": "Capture high-resolution photos of Yingxue Lake to monitor water levels and surrounding vegetation. Professional context: Acquire multi-angle, high-resolution imagery of Yingxue Lake for precise analysis of the current water line, water eutrophication levels, and shoreline vegetation coverage and health. The data will be used for academic research. User context: The data for this project is critical; we need to analyze the relationship between the lake water and the surrounding vegetation. Fly over there and capture the water color, water level, and the condition of the plants on the shore crystal clear. Not a single detail can be missed.", "task_types": ["Survey task"], "subtask_count": 1}, {"task_id": "5", "description": "Create a detailed map of the basketball courts. Professional context: Perform an orthomosaic survey of the basketball courts to generate an ultra-high-precision map with a GSD no worse than 1.6 cm/pixel, intended for the fine analysis of ground line wear. User context: We need to issue a formal report on the basketball court's maintenance, and we need the absolute clearest photo as a base map. It must be sharp enough to see every single scratch and the tiniest crack on the ground. There's zero room for ambiguity.", "task_types": ["Survey task"], "subtask_count": 1}, {"task_id": "7", "description": "Perform a comprehensive scan of the teaching building exterior.\n\nProfessional context: Execute a high-precision 3D reconstruction scan of the teaching building's facade. Ensure complete data integrity for archival purposes.\n\nUser context: We need to create a digital model of this teaching building for our archives, so every facade and every corner must be captured. Nothing can be missed, to ensure the final model is both complete and accurate.", "task_types": ["Structure scan task"], "subtask_count": 1}, {"task_id": "8", "description": "Fly along the main road and capture photos to document campus traffic flow. Professional context: Execute a rapid fly-through along the main road to roughly document peak-hour traffic flow. The objective is to capture the general traffic distribution without need for fine-detail recognition. User context: I just want to see how congested the main road gets during the evening rush hour. Just do a quick pass from one end to the other. I just need a general idea, no need to take careful shots.", "task_types": ["Corridor scan task"], "subtask_count": 1}, {"task_id": "9", "description": "Photograph the laboratory building from multiple angles to document its current state.\n\nProfessional context: Capture a set of high-quality imagery of the laboratory building via multi-angle, multi-altitude orbital flights to comprehensively document the building's current external condition for submission to the engineering department.\n\nUser context: The engineering department needs a status report on the lab building. Please help me get shots of all sides of the building, make them clear, and ensure the information is complete so they can't say our submission is inadequate.", "task_types": ["Structure scan task"], "subtask_count": 1}, {"task_id": "10", "description": "Inspect the cafeteria roof and surrounding area for potential maintenance issues.\n\nProfessional context: Conduct a standard inspection of the cafeteria roof and its surrounding ancillary facilities (e.g., exhaust ducts, AC units) to identify all visible potential maintenance risks and generate an inspection checklist.\n\nUser context: It's safety inspection month again. Can you check the cafeteria roof and all those pipes and equipment next to it? Look for anything loose or rusty so I can fill out the report form.", "task_types": ["Structure scan task"], "subtask_count": 1}, {"task_id": "11", "description": "Create an orthomosaic map of the entire campus.\n\nProfessional context: Execute a campus-wide aerial survey mission to generate a complete, seamlessly stitched, high-precision orthomosaic map, to be used for future digital campus planning.\n\nUser context: This is one of the most important projects for our university, to create a brand new, definitive digital map. You must cover the entire campus, and the final stitched map cannot have any misalignments or blurring. All future planning will rely on this map.", "task_types": ["Survey task"], "subtask_count": 1}, {"task_id": "12", "description": "Capture aerial photos of the campus entrance and main roads for campus navigation materials.\n\nProfessional context: To produce campus navigation materials, please capture aerial imagery of the main campus entrance and key roads. The imagery should be well-composed and clear, explicitly showing road directions and landmark buildings.\n\nUser context: We want to create a nice and practical navigation guide for new students. Can you help me get some shots of the main gate and the main roads? They need to be clear enough for people to understand the layout at a glance.", "task_types": ["Corridor scan task", "Structure scan task"], "subtask_count": 2}]