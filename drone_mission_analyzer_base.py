import json
import os
import sys
from openai import OpenAI
from typing import Dict, List, Any, Optional
import argparse

class DroneMissionAnalyzer:
    """
    无人机航拍任务分析器，使用大语言模型来分析和分类任务
    """

    def __init__(self, api_key: str, model: str = "gpt-4o", base_url: str = "https://api.openai.com/v1"):
        """
        初始化分析器

        Args:
            api_key: LLM API密钥
            model: 使用的模型名称
            base_url: API基础URL
        """
        self.api_key = api_key
        self.model = model
        self.base_url = base_url
        self.client = OpenAI(
            base_url=base_url,
            api_key=api_key
        )

    def load_mission_data(self, file_path: str) -> Dict:
        """
        加载任务数据JSON文件

        Args:
            file_path: JSON文件路径

        Returns:
            解析后的任务数据字典
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return json.load(file)
        except Exception as e:
            print(f"Error loading mission data: {e}")
            sys.exit(1)

    def generate_prompt(self, task_id: str, description: str, target_areas: List[Dict],
                        home_position: List, flight_restricted_area: Dict) -> str:
        """
        Generate a prompt for the LLM

        Args:
            task_id: Task ID
            description: Task description
            target_areas: List of target areas
            home_position: Starting position
            flight_restricted_area: Flight restricted area

        Returns:
            Formatted prompt string
        """
        # Create simplified target area information for easy reference
        target_areas_simplified = []
        for area in target_areas:
            target_areas_simplified.append({
                "id": area["id"],
                "name": area["name"],
                "type": area["type"]
            })

        target_areas_simple_text = json.dumps(target_areas_simplified, indent=2)
        target_areas_text = json.dumps(target_areas, indent=2)


        prompt = f"""
                # Role

                You are an expert UAV Remote Sensing Mission Planning Assistant.

                # Objective

                Your core objective is to conduct a comprehensive task analysis based on user requirements and available area information, and respond with a single, structured JSON object.

                # Workflow

                1. **Analyze Target Areas**:
                - Carefully parse the `user requirement` to identify all relevant `task target areas`.
                
                The user request may:
                - Not explicitly mention a specific **task target area**;
                - Correspond to multiple **task target areas**;
                - Specify a **task target area** not included in the **available target areas** list.

                2. **Create Subtasks**:
                - For all cases (explicit, implicit, or missing from the list), you must create a subtasks field for each task target area to perform task decomposition.

                3. **Decompose and Evaluate Subtasks**:
                - For each subtask, perform the following evaluations:
                    
                    - **Type Classification**: Assign the most appropriate task type from the predefined `Task Types list`.
                    
                    - **Feasibility Analysis**: Using your domain knowledge of UAV remote sensing and photogrammetry, determine if the subtask is feasible and if its parameters are reasonable.
                    
                    - **Weight Assignment**: Assign weights that will be used to configure UAV flight parameters (e.g., GSD, Frontal Overlap, Side Overlap). The weights must be a decimal number within the range `[-1.0, 1.0]`.
                    
                    - **`wtask` (Task Weight)**: Evaluates the intrinsic need for detailed observation of the target area itself.
                        - Set to `(0, 1.0]` if the target requires fine detail (e.g., complex structures). The higher the required detail, the larger the value.
                        - Set to `[-1.0, 0)` if the target is simple (e.g., a large, flat field). The simpler the area, the smaller the value.
                    
                    - **`wsemantic` (Semantic Weight)**: Evaluates the semantic intent in the user's description.
                        - Set to `(0, 1.0]` for words like "detailed," "precise," "high-quality," or "comprehensive." The stronger the semantic intent, the larger the value.
                        - Set to `[-1.0, 0)` for words like "quick," "rough," "simple," or "efficient." The stronger the semantic intent, the smaller the value.

                # Current Mission Information

                - **Task ID**: `{task_id}`
                - **User requirement**: `{description}`
                - Available Target Areas (Simple):
                
                {target_areas_simple_text}

                - Available Target Areas (Detailed):
                
                {target_areas_text}

                - **Task Types List**:
                1. (Simple waypoint task) - Navigate between specific waypoints
                2. (Survey task) - Systematically scan an area for mapping or monitoring
                3. (Corridor scan task) - Scan along linear features (roads, pipelines, rivers)
                4. (Structure scan task) - Detailed scanning around buildings (buildings, monuments)

                # Response Formatting Requirements

                - You must respond strictly with a valid JSON object matching the structure below.
                - Do not include any explanatory text or notes before or after the JSON object.

                ```json
                {{
                    "task_id": "{task_id}",
                    "description": "{description}",
                    "home_position": {json.dumps(home_position)},
                    "flight_restricted_area": {json.dumps(flight_restricted_area)},
                    "task_types": [
                        // List of all task types involved in this mission
                    ],
                    "subtasks": [
                        {{
                            "subtask_id": "1",
                            "type": "The type of the task, must be one of ['Simple waypoint task', 'Survey task', 'Corridor scan task', 'Structure scan task']",
                            "target_area_id": "The ID of the target area, if applicable",
                            "target_area_name": "The name of the target area, if applicable",
                            "feasible": true,
                            "geometry": {{
                                "type": "The geometry type, one of ['Polygon', 'LineString', 'MultiPoint']",
                                "coordinates": [
                                    // Coordinates array based on the geometry type
                                ]
                            }},
                            "parameters": {{
                                // Parameters vary by task type:
                                // The parameters in the user request regarding "frontal_overlap", "lateral_overlap", and "gsd" should also be saved, and the field names must remain as "frontal_overlap", "lateral_overlap", and "gsd".
                                // For Survey, Corridor scan, Structure scan: "wtask", "wsemantic" are required.
                                // For Corridor scan: "width" is also required.
                                // For Structure scan: "height" is also required.
                                // For Simple waypoint task: "height" is optional (default: 30).
                            }},
                            "error": "Provide an error message if necessary parameters are missing or invalid."
                        }}
                    ]
                }}
                ```
                """
        return prompt

    def convert_weights_to_parameters(self, wtask: float, wsemantic: float, alpha: float = 0.63) -> Dict[str, float]:
        """
        根据权重计算原始参数

        Args:
            wtask: 任务复杂度权重 [-1, 1]
            wsemantic: 语义关键词权重 [-1, 1]
            alpha: 权重平衡因子 [0, 1]

        Returns:
            包含计算出的参数的字典
        """
        # 计算复合权重
        w_total = alpha * wtask + (1 - alpha) * wsemantic

        # 标准参数
        p_standard = {
            'frontal_overlap': 75.0,  # %
            'lateral_overlap': 65.0,  # %
            'gsd': 4.4,  # cm/pixel
            'height': 30.0  # m
        }

        # 参数范围
        p_ranges = {
            'frontal_overlap': {'min': 60.0, 'max': 90.0},
            'lateral_overlap': {'min': 50.0, 'max': 80.0},
            'gsd': {'min': 0.8, 'max': 8.0},
            'height': {'min': 10.0, 'max': 120.0}
        }

        # 方向因子：overlap为+1（权重越高，重叠越高），GSD和height为-1（权重越高，值越小）
        direction_factors = {
            'frontal_overlap': 1,
            'lateral_overlap': 1,
            'gsd': -1,
            'height': -1
        }

        # 计算最终参数
        calculated_params = {}
        for param_name, standard_value in p_standard.items():
            p_min = p_ranges[param_name]['min']
            p_max = p_ranges[param_name]['max']
            d = direction_factors[param_name]

            # 应用公式: p = p_standard + 0.5 * d * w_total * (p_max - p_min)
            calculated_value = standard_value + 0.5 * d * w_total * (p_max - p_min)

            # 确保值在允许范围内
            calculated_value = max(p_min, min(p_max, calculated_value))

            # 根据参数类型进行适当的舍入
            if param_name in ['frontal_overlap', 'lateral_overlap']:
                calculated_params[param_name] = round(calculated_value, 1)
            elif param_name == 'gsd':
                calculated_params[param_name] = round(calculated_value, 2)
            else:  # height
                calculated_params[param_name] = round(calculated_value, 1)

        return calculated_params

    def call_llm_api(self, prompt: str) -> str:
        """
        使用OpenAI客户端库调用大语言模型API

        Args:
            prompt: 提示字符串

        Returns:
            API响应内容
        """
        try:
            completion = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a drone mission planning assistant, specializing in analyzing mission requirements and categorizing them into specific mission types, as well as designing appropriate aerial photography parameters for the mission."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3
            )

            return completion.choices[0].message.content

        except Exception as e:
            print(f"Error calling LLM API: {e}")
            raise

    def parse_llm_response(self, response: str, task_id: str, description: str) -> Dict:
        """
        解析LLM响应为所需的JSON格式

        Args:
            response: LLM响应文本
            task_id: 任务ID（用于回退）
            description: 任务描述（用于回退）

        Returns:
            解析后的任务JSON
        """
        try:
            # 提取JSON部分
            json_start = response.find('{')
            json_end = response.rfind('}') + 1

            if json_start == -1 or json_end == 0:
                raise ValueError("响应中未找到JSON")

            json_str = response[json_start:json_end]

            # 解析JSON
            task_json = json.loads(json_str)

            # 确保JSON具有所需的结构
            if "task_id" not in task_json:
                task_json["task_id"] = task_id

            if "description" not in task_json:
                task_json["description"] = description

            if "subtasks" not in task_json:
                task_json["subtasks"] = []

            if "task_types" not in task_json:
                # 尝试从子任务派生任务类型
                task_types = set()
                for subtask in task_json.get("subtasks", []):
                    if "type" in subtask:
                        task_types.add(subtask["type"])
                task_json["task_types"] = list(task_types)

            # 转换权重为原始参数
            for subtask in task_json.get("subtasks", []):
                if "parameters" in subtask and subtask.get("feasible", True):
                    params = subtask["parameters"]

                    # 检查是否包含权重参数
                    if "wtask" in params and "wsemantic" in params:
                        wtask = params["wtask"]
                        wsemantic = params["wsemantic"]

                        # 计算原始参数
                        calculated_params = self.convert_weights_to_parameters(wtask, wsemantic)

                        # 根据任务类型添加相应的参数，但只添加用户未明确指定的参数
                        task_type = subtask.get("type", "")
                        if task_type in ["Survey task", "Corridor scan task", "Structure scan task"]:
                            # 只有当用户没有明确指定这些参数时，才使用计算出的值
                            if "frontal_overlap" not in params:
                                params["frontal_overlap"] = calculated_params["frontal_overlap"]
                            if "lateral_overlap" not in params:
                                params["lateral_overlap"] = calculated_params["lateral_overlap"]
                            if "gsd" not in params:
                                params["gsd"] = calculated_params["gsd"]
                        # 保留原有的特殊参数
                        if task_type == "Corridor scan task" and "width" not in params:
                            # 如果没有width参数，保持原有的错误处理逻辑
                            pass

                        if task_type == "Structure scan task":
                            if "height" not in params:
                                # 如果没有height参数，使用计算出的height
                                params["height"] = calculated_params["height"]

                        if task_type == "Simple waypoint task":
                            if "height" not in params:
                                params["height"] = calculated_params["height"]

            return task_json

        except Exception as e:
            print(f"Error parsing LLM response: {e}")
            print(f"Response: {response}")

            # 创建最小有效JSON作为回退
            return {
                "task_id": task_id,
                "description": description,
                "task_types": ["unknown"],
                "task_summary": "无法完成任务分析。系统在处理您的请求时遇到了问题，无法生成有效的任务计划。请检查您的任务描述是否清晰，并确保所请求的目标区域在可用区域列表中。",
                "feasibility_analysis": "无法解析任务，请检查任务描述和可用区域",
                "subtasks": [],
                "error": str(e),
                "original_response": response
            }

    def save_results(self, results: List[Dict], output_dir: str) -> None:
        """
        将结果保存到JSON文件

        Args:
            results: 任务分析结果列表
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)

        # 保存每个任务到单独的文件
        for result in results:
            task_id = result["task_id"]
            task_file = f"{output_dir}/task_{task_id}.json"

            with open(task_file, 'w', encoding='utf-8') as file:
                json.dump(result, file, indent=2, ensure_ascii=False)

            print(f"Saved task {task_id} to {task_file}")

        # 保存所有任务到单个文件
        all_tasks_file = f"{output_dir}/all_tasks.json"
        with open(all_tasks_file, 'w', encoding='utf-8') as file:
            json.dump(results, file, indent=2, ensure_ascii=False)

        print(f"Saved all tasks to {all_tasks_file}")

        # 创建摘要文件
        summary = []
        for result in results:
            summary_item = {
                "task_id": result["task_id"],
                "description": result["description"],
                "task_types": result["task_types"],
                "subtask_count": len(result["subtasks"])
            }

            # 添加任务总结到摘要（如果存在）
            if "task_summary" in result:
                summary_item["task_summary"] = result["task_summary"]

            # 添加可行性分析到摘要（如果存在）
            if "feasibility_analysis" in result:
                summary_item["feasibility_analysis"] = result["feasibility_analysis"]

            summary.append(summary_item)

        summary_file = f"{output_dir}/tasks_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as file:
            json.dump(summary, file, indent=2, ensure_ascii=False)

        print(f"Saved summary to {summary_file}")

    def process_task(self, task: Dict, mission_prior: Dict) -> Dict:
        """
        处理单个任务

        Args:
            task: 任务数据
            mission_prior: 任务先验信息

        Returns:
            处理后的任务JSON
        """
        task_id = task["task_id"]
        description = task["description"]

        print(f"Processing task {task_id}: {description}")

        # 生成LLM提示
        prompt = self.generate_prompt(
            task_id,
            description,
            mission_prior["target_areas"],
            mission_prior["home_position"],
            mission_prior["flight_restricted_area"]
        )

        # 调用LLM API
        response = self.call_llm_api(prompt)

        # 解析响应
        task_json = self.parse_llm_response(response, task_id, description)

        print(f"Completed task {task_id}")
        return task_json

    def analyze_mission(self, input_file: str, output_dir: str) -> None:
        """
        分析整个任务数据

        Args:
            input_file: 输入JSON文件路径
            output_dir: 输出目录
        """
        print(f"Starting drone mission analysis")
        print(f"Input file: {input_file}")
        print(f"Output directory: {output_dir}")
        print(f"Using model: {self.model}")
        print(f"Using API base URL: {self.base_url}")

        # 加载任务数据
        mission_data = self.load_mission_data(input_file)
        print(f"Loaded mission data with {len(mission_data.get('user_requirements', []))} tasks")

        mission_prior = mission_data["mission_prior"]
        user_requirements = mission_data["user_requirements"]

        # 处理所有任务
        results = []
        for task in user_requirements:
            try:
                task_json = self.process_task(task, mission_prior)
                results.append(task_json)
            except Exception as e:
                print(f"Error processing task {task.get('task_id', 'unknown')}: {e}")

                # 添加错误条目到结果
                results.append({
                    "task_id": task.get("task_id", "unknown"),
                    "description": task.get("description", ""),
                    "error": str(e),
                    "task_types": ["error"],
                    "task_summary": "任务处理失败。系统在分析您的请求时遇到了技术问题，无法完成任务规划。请稍后重试或修改您的任务描述。",
                    "feasibility_analysis": "处理任务时发生错误，无法确定任务可行性",
                    "subtasks": []
                })

        # 保存结果
        self.save_results(results, output_dir)

        print(f"Analysis complete. Results saved to {output_dir}")

def analyze_mission():
    """主函数"""
    parser = argparse.ArgumentParser(description='分析无人机任务需求并将其分类为任务类型')
    parser.add_argument('--input_file', default="experiment/env_1/exp_env_1_test.json",help='包含任务数据的输入JSON文件路径')
    parser.add_argument('--output_dir', default="task_base/task_analysis",help='保存输出JSON文件的目录')
    # parser.add_argument('--api-key', default="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",help='LLM服务的API密钥')
    # parser.add_argument('--model', default="openai/gpt-5-mini", help='要使用的LLM模型 (默认: gpt-4o)')
    # parser.add_argument('--api-url', default="https://openrouter.ai/api/v1/", help='API端点URL')

    # anthropic/claude-3.7-sonnet        google/gemini-2.5-pro-preview    google/gemini-2.5-flash-preview
    parser.add_argument('--api-key', default="sk-df0cb63f37e240699f78a373ce764daa",help='LLM服务的API密钥')
    parser.add_argument('--model', default="deepseek-chat", help='要使用的LLM模型 (默认: gpt-4o)')
    parser.add_argument('--api-url', default="https://api.deepseek.com", help='API端点URL')

    args = parser.parse_args()

    # 创建分析器并运行
    analyzer = DroneMissionAnalyzer(args.api_key, args.model, args.api_url)
    analyzer.analyze_mission(args.input_file, args.output_dir)

if __name__ == "__main__":
    analyze_mission()
